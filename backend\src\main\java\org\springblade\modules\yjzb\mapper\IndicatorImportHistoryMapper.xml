<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorImportHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorImportHistoryResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="import_type" property="importType"/>
        <result column="template_type" property="templateType"/>
        <result column="file_name" property="fileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="period" property="period"/>
        <result column="total_count" property="totalCount"/>
        <result column="success_count" property="successCount"/>
        <result column="fail_count" property="failCount"/>
        <result column="import_status" property="importStatus"/>
        <result column="import_result" property="importResult"/>
        <result column="error_message" property="errorMessage"/>
        <result column="import_duration" property="importDuration"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 自定义分页查询 -->
    <select id="selectIndicatorImportHistoryPage" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO">
        SELECT 
            iih.*,
            cu.real_name AS createUserName
        FROM yjzb_indicator_import_history iih
        LEFT JOIN blade_user cu ON iih.create_user = cu.id
        WHERE iih.is_deleted = 0
        <if test="indicatorImportHistory.importType != null and indicatorImportHistory.importType != ''">
            AND iih.import_type = #{indicatorImportHistory.importType}
        </if>
        <if test="indicatorImportHistory.templateType != null and indicatorImportHistory.templateType != ''">
            AND iih.template_type = #{indicatorImportHistory.templateType}
        </if>
        <if test="indicatorImportHistory.fileName != null and indicatorImportHistory.fileName != ''">
            AND iih.file_name LIKE CONCAT('%', #{indicatorImportHistory.fileName}, '%')
        </if>
        <if test="indicatorImportHistory.period != null and indicatorImportHistory.period != ''">
            AND iih.period = #{indicatorImportHistory.period}
        </if>
        <if test="indicatorImportHistory.importStatus != null and indicatorImportHistory.importStatus != ''">
            AND iih.import_status = #{indicatorImportHistory.importStatus}
        </if>
        ORDER BY iih.create_time DESC
    </select>

    <!-- 根据条件分页查询导入历史记录 -->
    <select id="selectIndicatorImportHistoryPageByCondition" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO">
        SELECT 
            iih.*,
            cu.real_name AS createUserName
        FROM yjzb_indicator_import_history iih
        LEFT JOIN blade_user cu ON iih.create_user = cu.id
        ${ew.customSqlSegment}
    </select>

</mapper>
