#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      ##将docker脚本部署的redis服务映射为宿主机ip
      ##生产环境推荐使用阿里云高可用redis服务并设置密码
      host: redis
      port: 6379
      password:
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    url: *****************************************
    username: root
    password: Ystech@2025

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://redis:6379
    password:
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html


#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://minio:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: OkL85reOLjtr30xaPjtV
  #密钥key
  secret-key: OR7oala7v39rHB4CC7r1zR9479FDZVSXmYlD4ftU
  #存储桶
  bucket-name: images

# Dify配置
dify:
  api:
    base-url: http://***********
    key: dataset-yIxMQAPhYKzfioH9LRSjZ0bK
    timeout: 30000
    agentkey:
      analysisagent: app-FdeOw28E2ZjVR4e0HhC3ziTe

  # 支持的文件类型
  supported-file-types:
    - pdf
    - doc
    - docx
    - xls
    - xlsx
    - txt
    - md
    - csv

  # 文件大小限制（字节）
  max-file-size: 52428800  # 50MB
