/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO;
import org.springblade.modules.yjzb.excel.IndicatorValuesExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorValuesWrapper;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorAiAnalysisService;
import org.springblade.modules.yjzb.service.IIndicatorImportService;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;

import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 指标数据 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorValues")
@Tag(name = "指标数据", description = "指标数据接口")
public class IndicatorValuesController extends BladeController {

    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorAiAnalysisService indicatorAiAnalysisService;
    private final IIndicatorImportService indicatorImportService;

    /**
     * 指标数据 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorValues")
    public R<IndicatorValuesVO> detail(IndicatorValuesEntity indicatorValues) {
        IndicatorValuesEntity detail = indicatorValuesService.getOne(Condition.getQueryWrapper(indicatorValues));
        return R.data(IndicatorValuesWrapper.build().entityVO(detail));
    }

    /**
     * 指标数据 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorValues")
    public R<IPage<IndicatorValuesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValues,
            Query query) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorValuesWrapper.build().indicatorValuesQuery(indicatorValues);
        IPage<IndicatorValuesEntity> pages = indicatorValuesService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicatorValues, IndicatorValuesEntity.class));
        return R.data(IndicatorValuesWrapper.build().pageVO(pages));
    }

    /**
     * 指标数据 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorValues")
    public R<IPage<IndicatorValuesVO>> page(IndicatorValuesVO indicatorValues, Query query) {
        IPage<IndicatorValuesVO> pages = indicatorValuesService.selectIndicatorValuesPage(Condition.getPage(query),
                indicatorValues);
        return R.data(pages);
    }

    /**
     * 指标数据 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicatorValues")
    public R<Boolean> save(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.save(indicatorValues));
    }

    /**
     * 指标数据 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicatorValues")
    public R<Boolean> update(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.updateById(indicatorValues));
    }

    /**
     * 指标数据 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicatorValues")
    public R<Boolean> submit(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.saveOrUpdate(indicatorValues));
    }

    /**
     * 指标数据 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R<Boolean> remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorValuesService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 根据指标类型分页查询指标数据
     */
    @GetMapping("/listByType")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "根据指标类型分页查询", description = "传入指标类型ID等参数")
    public R<IPage<IndicatorValuesVO>> listByType(
            @Parameter(description = "指标类型ID", required = true) @RequestParam Long indicatorTypeId,
            @Parameter(description = "数据期间，格式：YYYY-MM", required = false) @RequestParam(required = false) String period,
            @Parameter(description = "数据来源", required = false) @RequestParam(required = false) String dataSource,
            Query query) {

        IPage<IndicatorValuesVO> pages = indicatorValuesService.selectIndicatorValuesByType(
                Condition.getPage(query), indicatorTypeId, period, dataSource);
        return R.data(pages);
    }

    /**
     * 统计指定月份和指标类型的指标数值
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "统计指标数值", description = "统计指定月份和指标类型的指标数值")
    public R<Map<String, Object>> getStatistics(
            @Parameter(description = "数据期间，格式：YYYY-MM", required = true) @RequestParam String period,
            @Parameter(description = "指标类型ID", required = true) @RequestParam Long indicatorTypeId) {

        Map<String, Object> statistics = indicatorValuesService.getIndicatorStatistics(period, indicatorTypeId);
        return R.data(statistics);
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicatorValues")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "导出数据", description = "传入indicatorValues")
    public void exportIndicatorValues(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValues,
            BladeUser bladeUser, HttpServletResponse response) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorValuesWrapper.build().indicatorValuesQuery(indicatorValues);
        QueryWrapper<IndicatorValuesEntity> queryWrapper = Condition.getQueryWrapper(indicatorValues,
                IndicatorValuesEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(IndicatorValues::getTenantId,
        // bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(IndicatorValuesEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<IndicatorValuesExcel> list = indicatorValuesService.exportIndicatorValues(queryWrapper);
        ExcelUtil.export(response, "指标数据数据" + DateUtil.time(), "指标数据数据表", list, IndicatorValuesExcel.class);
    }

    /**
     * 重新开始AI分析：
     * - 使用该条指标数据的数值作为输入启动Dify工作流
     * - 将workflowRunId等信息写入AI解读分析表
     * - 开启后台轮询每3分钟查询一次，完成后回写结果
     */
    @PostMapping("/restart-ai-analysis")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "重新开始AI分析", description = "参数：indicatorValueId")
    public R<Long> restartAiAnalysis(@RequestParam Long indicatorValueId) {
        Long aiRecordId = indicatorAiAnalysisService.restartAnalysisForIndicatorValue(indicatorValueId);
        if (aiRecordId == null) {
            return R.fail("启动AI分析失败：请检查Dify配置(dify.api.base-url, dify.api.agentkey.analysisagent)是否正确且服务可访问");
        }
        return R.data(aiRecordId);
    }

    /**
     * 当月某类型AI指标解读（总体分析）
     * 传入：indicatorTypeId, period(YYYY-MM)
     * 聚合：同类型该月所有指标数据、同类型年度预算之和、当年累计支出和完成进度
     */
    @PostMapping("/monthlyTypeAnalysis/restart")
    @ApiOperationSupport(order = 13)
    @Operation(summary = "当月某类型AI指标解读(重启并记录)", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<Long> restartMonthlyTypeAnalysis(@RequestParam Long indicatorTypeId, @RequestParam String period) {
        Long id = indicatorAiAnalysisService.restartTypeMonthlyAnalysis(indicatorTypeId, period);
        return id == null ? R.fail("AI总体解读失败") : R.data(id);
    }

    @GetMapping("/monthlyTypeAnalysis/latest")
    @ApiOperationSupport(order = 14)
    @Operation(summary = "查询当月某类型AI指标解读结果", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity> latestMonthlyTypeAnalysis(
            @RequestParam Long indicatorTypeId, @RequestParam String period) {
        var entity = indicatorAiAnalysisService.getLatestByIndicatorAndPeriod(indicatorTypeId, period);
        return R.data(entity);
    }

    /**
     * 查询最新AI分析结果（按指标数据ID映射 indicatorId+period）
     */
    @GetMapping("/latest-ai-analysis")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "查询最新AI分析结果", description = "参数：indicatorValueId")
    public R<org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity> latestAiAnalysis(
            @RequestParam Long indicatorValueId) {
        var entity = indicatorAiAnalysisService.getLatestByIndicatorValueId(indicatorValueId);
        return R.data(entity);
    }

    /**
     * Excel数据导入
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperationSupport(order = 15)
    @Operation(summary = "Excel数据导入", description = "上传Excel文件导入指标数据")
    public R<Map<String, Object>> importExcelData(
            @Parameter(description = "Excel文件", required = true) @RequestPart("file") MultipartFile file,
            @Parameter(description = "模板类型：profit(利润表), balance(资产负债表), expense(三项费用), tax(锐利指标明细表)", required = true) @RequestParam("templateType") String templateType,
            @Parameter(description = "数据期间，格式YYYY-MM", required = true) @RequestParam("period") String period) {

        try {
            Map<String, Object> result = indicatorImportService.importExcelData(file, templateType, period);
            if ((Boolean) result.get("success")) {
                return R.data(result, "导入成功");
            } else {
                return R.fail("导入失败").data(result);
            }
        } catch (Exception e) {
            return R.fail("导入异常: " + e.getMessage());
        }
    }
}
