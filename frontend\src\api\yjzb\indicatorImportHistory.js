import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorImportHistory/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicatorImportHistory/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicatorImportHistory/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicatorImportHistory/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicatorImportHistory/submit',
    method: 'post',
    data: row
  })
}

// 根据搜索条件分页查询导入历史记录
export const searchImportHistory = (current, size, params = {}) => {
  return request({
    url: '/yjzb/indicatorImportHistory/search',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

// 自定义分页查询
export const getPageList = (current, size, params = {}) => {
  return request({
    url: '/yjzb/indicatorImportHistory/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
