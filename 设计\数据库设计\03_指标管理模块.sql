-- =====================================================
-- 指标管理模块数据库表结构
-- 模块说明：指标类型管理、指标数据管理等功能
-- 创建时间：2024年
-- 数据库：PostgreSQL
-- =====================================================

-- =====================================================
-- 1. 指标类型表
-- =====================================================
CREATE TABLE yjzb_indicator_types (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    data_type VARCHAR(50) NOT NULL,
    unit VARCHAR(50),
    calculation_formula TEXT,
    data_source_config JSONB,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_indicator_types IS '指标类型表 - 定义各类指标的基本信息和计算规则';

-- 添加字段注释
COMMENT ON COLUMN yjzb_indicator_types.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_types.type_name IS '指标类型名称';
COMMENT ON COLUMN yjzb_indicator_types.type_code IS '指标类型编码（唯一）';
COMMENT ON COLUMN yjzb_indicator_types.description IS '指标类型描述';
COMMENT ON COLUMN yjzb_indicator_types.data_type IS '数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）';
COMMENT ON COLUMN yjzb_indicator_types.unit IS '计量单位';
COMMENT ON COLUMN yjzb_indicator_types.calculation_formula IS '计算公式（用于复合指标）';
COMMENT ON COLUMN yjzb_indicator_types.data_source_config IS '数据源配置（JSON格式，包含数据源类型、连接信息等）';
COMMENT ON COLUMN yjzb_indicator_types.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_types.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_types.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_types.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_types.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_types.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_types.is_deleted IS '删除标记（0-未删除，1-已删除）';
-- =====================================================
-- 2. 指标表
-- =====================================================
CREATE TABLE yjzb_indicator (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
		indicator_type_id BIGINT,
    data_type VARCHAR(50) NOT NULL,
    unit VARCHAR(50),
    calculation_formula TEXT,
    data_source_config JSONB,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_indicator IS '指标类型表 - 定义各类指标的基本信息和计算规则';

-- 添加字段注释
COMMENT ON COLUMN yjzb_indicator.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator.name IS '指标类型名称';
COMMENT ON COLUMN yjzb_indicator.code IS '指标类型编码（唯一）';
COMMENT ON COLUMN yjzb_indicator.indicator_type_id IS '指标类型';
COMMENT ON COLUMN yjzb_indicator.description IS '指标类型描述';
COMMENT ON COLUMN yjzb_indicator.data_type IS '数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）';
COMMENT ON COLUMN yjzb_indicator.unit IS '计量单位';
COMMENT ON COLUMN yjzb_indicator.calculation_formula IS '计算公式（用于复合指标）';
COMMENT ON COLUMN yjzb_indicator.data_source_config IS '数据源配置（JSON格式，包含数据源类型、连接信息等）';
COMMENT ON COLUMN yjzb_indicator.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 3. 指标数据表
-- =====================================================
CREATE TABLE yjzb_indicator_values (
    id BIGSERIAL PRIMARY KEY,
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator_types(id),
    period VARCHAR(20) NOT NULL,
    value NUMERIC(18,6),
    dimensions TEXT,
    data_source VARCHAR(100),
    calculation_status VARCHAR(20) DEFAULT 'PENDING',
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_indicator_values IS '指标数据表 - 存储各指标的具体数值';

-- 添加字段注释
COMMENT ON COLUMN yjzb_indicator_values.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_values.indicator_id IS '指标类型ID（关联指标类型表）';
COMMENT ON COLUMN yjzb_indicator_values.period IS '数据期间（格式：YYYY-MM）';
COMMENT ON COLUMN yjzb_indicator_values.value IS '指标数值';
COMMENT ON COLUMN yjzb_indicator_values.dimensions IS '维度信息（JSON格式，存储多维度数据）';
COMMENT ON COLUMN yjzb_indicator_values.data_source IS '数据来源';
COMMENT ON COLUMN yjzb_indicator_values.calculation_status IS '计算状态（PENDING-待计算、PROCESSING-计算中、COMPLETED-已完成、FAILED-失败）';
COMMENT ON COLUMN yjzb_indicator_values.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_values.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_values.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_values.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_values.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_values.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_values.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 3. 创建索引
-- =====================================================

-- 指标数据表索引
CREATE INDEX idx_yjzb_indicator_values_indicator_period ON yjzb_indicator_values(indicator_id, period DESC);
CREATE INDEX idx_yjzb_indicator_values_period ON yjzb_indicator_values(period);
CREATE INDEX idx_yjzb_indicator_values_calculation_status ON yjzb_indicator_values(calculation_status);

-- =====================================================
-- 4. 初始化基础数据
-- =====================================================

-- 插入指标类型基础数据
INSERT INTO yjzb_indicator_types (type_name, type_code, description, data_type, unit, create_user, create_dept, create_time, status, is_deleted) VALUES
('数值型指标', 'NUMERIC', '纯数值类型指标', 'NUMERIC', '元', 1, 1, NOW(), 1, 0),
('百分比指标', 'PERCENTAGE', '百分比类型指标', 'PERCENTAGE', '%', 1, 1, NOW(), 1, 0),
('枚举指标', 'ENUM', '枚举类型指标', 'ENUM', '', 1, 1, NOW(), 1, 0),
('复合指标', 'COMPOSITE', '由多个指标计算得出', 'COMPOSITE', '', 1, 1, NOW(), 1, 0);

-- =====================================================
-- 指标管理模块表结构创建完成
-- ===================================================== 

-- =====================================================
-- 5. 指标年度预算表
-- 说明：用于管理每个指标在某年度的预算、报备及使用情况
-- =====================================================
CREATE TABLE yjzb_indicator_annual_budget (
    id BIGSERIAL PRIMARY KEY,
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator(id),
    indicator_name VARCHAR(100) NOT NULL,
    year INT NOT NULL,
    initial_budget NUMERIC(18,6) DEFAULT 0,
    initial_reported NUMERIC(18,6) DEFAULT 0,
    midyear_budget NUMERIC(18,6) DEFAULT 0,
    midyear_reported NUMERIC(18,6) DEFAULT 0,
    current_used NUMERIC(18,6) DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0,
    UNIQUE (indicator_id, year)
);

-- 表注释
COMMENT ON TABLE yjzb_indicator_annual_budget IS '指标年度预算表 - 存储每年预算、报备及当前使用情况';

-- 字段注释
COMMENT ON COLUMN yjzb_indicator_annual_budget.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_annual_budget.indicator_id IS '指标ID（关联指标表）';
COMMENT ON COLUMN yjzb_indicator_annual_budget.indicator_name IS '指标名称（为历史保留冗余）';
COMMENT ON COLUMN yjzb_indicator_annual_budget.year IS '年份（YYYY）';
COMMENT ON COLUMN yjzb_indicator_annual_budget.initial_budget IS '年初预算数';
COMMENT ON COLUMN yjzb_indicator_annual_budget.initial_reported IS '年初报备数';
COMMENT ON COLUMN yjzb_indicator_annual_budget.midyear_budget IS '中期调整预算数';
COMMENT ON COLUMN yjzb_indicator_annual_budget.midyear_reported IS '中期调整报备数';
COMMENT ON COLUMN yjzb_indicator_annual_budget.current_used IS '当前使用数';
COMMENT ON COLUMN yjzb_indicator_annual_budget.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_annual_budget.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_annual_budget.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_annual_budget.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_annual_budget.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_annual_budget.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_annual_budget.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- 索引
CREATE INDEX idx_yjzb_indicator_annual_budget_indicator_id ON yjzb_indicator_annual_budget(indicator_id);
CREATE INDEX idx_yjzb_indicator_annual_budget_year ON yjzb_indicator_annual_budget(year);

-- =====================================================
-- 6. 指标AI解读分析表
-- 说明：记录每个指标在某年月由AI生成的解读与分析结果
-- =====================================================
CREATE TABLE yjzb_indicator_ai_analysis (
    id BIGSERIAL PRIMARY KEY,
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator(id),
    period VARCHAR(20) NOT NULL,
    input_params TEXT,
    execute_time TIMESTAMP(6) DEFAULT NOW(),
    execute_status VARCHAR(20) DEFAULT 'RUNNING',
    result TEXT,
    workflow_run_id VARCHAR(100),
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 表注释
COMMENT ON TABLE yjzb_indicator_ai_analysis IS '指标AI解读分析表 - 存储AI对指标数据的解读/分析结果';

-- 字段注释
COMMENT ON COLUMN yjzb_indicator_ai_analysis.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.indicator_id IS '指标ID（关联指标表）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.period IS '数据期间（格式：YYYY-MM）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.input_params IS '传入参数（JSON格式）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.execute_time IS '执行时间';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.execute_status IS '执行状态（PENDING、PROCESSING、COMPLETED、FAILED）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.result IS '执行结果（JSON格式，含解读/分析内容等）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.workflow_run_id IS '工作流运行ID（workflowRunId）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_ai_analysis.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- 索引
CREATE INDEX idx_yjzb_indicator_ai_analysis_indicator_period ON yjzb_indicator_ai_analysis(indicator_id, period DESC);
CREATE INDEX idx_yjzb_indicator_ai_analysis_status ON yjzb_indicator_ai_analysis(execute_status);
CREATE INDEX idx_yjzb_indicator_ai_analysis_execute_time ON yjzb_indicator_ai_analysis(execute_time);
CREATE INDEX idx_yjzb_indicator_ai_analysis_workflow_id ON yjzb_indicator_ai_analysis(workflow_run_id);

-- =====================================================
-- 指标管理模块表结构创建完成（含年度预算表）
-- =====================================================

-- =====================================================
-- 7. 指标预测结果表
-- 说明：用于存放每个指标在某年度2-12月的预测值
-- =====================================================
CREATE TABLE yjzb_indicator_forecast (
    id BIGSERIAL PRIMARY KEY,
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator(id),
    indicator_name VARCHAR(100),
    year INT NOT NULL,
    forecast_m02 NUMERIC(18,6) DEFAULT 0,
    forecast_m03 NUMERIC(18,6) DEFAULT 0,
    forecast_m04 NUMERIC(18,6) DEFAULT 0,
    forecast_m05 NUMERIC(18,6) DEFAULT 0,
    forecast_m06 NUMERIC(18,6) DEFAULT 0,
    forecast_m07 NUMERIC(18,6) DEFAULT 0,
    forecast_m08 NUMERIC(18,6) DEFAULT 0,
    forecast_m09 NUMERIC(18,6) DEFAULT 0,
    forecast_m10 NUMERIC(18,6) DEFAULT 0,
    forecast_m11 NUMERIC(18,6) DEFAULT 0,
    forecast_m12 NUMERIC(18,6) DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0,
    UNIQUE (indicator_id, year)
);

-- 表注释
COMMENT ON TABLE yjzb_indicator_forecast IS '指标预测结果表 - 存储每年2-12月预测值';

-- 字段注释
COMMENT ON COLUMN yjzb_indicator_forecast.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_forecast.indicator_id IS '指标ID（关联指标表）';
COMMENT ON COLUMN yjzb_indicator_forecast.indicator_name IS '指标名称（为历史保留冗余）';
COMMENT ON COLUMN yjzb_indicator_forecast.year IS '年份（YYYY）';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m02 IS '2月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m03 IS '3月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m04 IS '4月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m05 IS '5月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m06 IS '6月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m07 IS '7月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m08 IS '8月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m09 IS '9月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m10 IS '10月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m11 IS '11月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.forecast_m12 IS '12月预测值';
COMMENT ON COLUMN yjzb_indicator_forecast.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_forecast.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_forecast.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_forecast.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_forecast.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_forecast.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_forecast.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- 索引
CREATE INDEX idx_yjzb_indicator_forecast_indicator_id ON yjzb_indicator_forecast(indicator_id);
CREATE INDEX idx_yjzb_indicator_forecast_year ON yjzb_indicator_forecast(year);

-- =====================================================
-- 指标管理模块表结构创建完成（含年度预算表与预测结果表）
-- =====================================================