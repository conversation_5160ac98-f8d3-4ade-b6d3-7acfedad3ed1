/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.mapper.IndicatorImportHistoryMapper;
import org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO;
import org.springblade.modules.yjzb.service.IIndicatorImportHistoryService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 指标导入历史记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Service
public class IndicatorImportHistoryServiceImpl extends BaseServiceImpl<IndicatorImportHistoryMapper, IndicatorImportHistoryEntity> implements IIndicatorImportHistoryService {

	private final ObjectMapper objectMapper = new ObjectMapper();

	@Override
	public IPage<IndicatorImportHistoryVO> selectIndicatorImportHistoryPage(IPage<IndicatorImportHistoryVO> page, IndicatorImportHistoryVO indicatorImportHistory) {
		return page.setRecords(baseMapper.selectIndicatorImportHistoryPage(page, indicatorImportHistory));
	}

	@Override
	public IPage<IndicatorImportHistoryVO> selectIndicatorImportHistoryPageByCondition(
		IPage<IndicatorImportHistoryVO> page,
		String searchKeyword,
		String importType,
		String templateType,
		String importStatus,
		String period
	) {
		LambdaQueryWrapper<IndicatorImportHistoryEntity> queryWrapper = new LambdaQueryWrapper<>();
		
		// 搜索关键词（文件名、模板类型）
		if (Func.isNotBlank(searchKeyword)) {
			queryWrapper.and(wrapper -> wrapper
				.like(IndicatorImportHistoryEntity::getFileName, searchKeyword)
				.or()
				.like(IndicatorImportHistoryEntity::getTemplateType, searchKeyword)
			);
		}
		
		// 导入类型
		if (Func.isNotBlank(importType)) {
			queryWrapper.eq(IndicatorImportHistoryEntity::getImportType, importType);
		}
		
		// 模板类型
		if (Func.isNotBlank(templateType)) {
			queryWrapper.eq(IndicatorImportHistoryEntity::getTemplateType, templateType);
		}
		
		// 导入状态
		if (Func.isNotBlank(importStatus)) {
			queryWrapper.eq(IndicatorImportHistoryEntity::getImportStatus, importStatus);
		}
		
		// 数据期间
		if (Func.isNotBlank(period)) {
			queryWrapper.eq(IndicatorImportHistoryEntity::getPeriod, period);
		}
		
		// 按创建时间倒序
		queryWrapper.orderByDesc(IndicatorImportHistoryEntity::getCreateTime);
		
		return baseMapper.selectIndicatorImportHistoryPageByCondition(page, queryWrapper);
	}

	@Override
	public Long recordImportHistory(String importType, String templateType, String fileName, Long fileSize, 
		String period, Map<String, Object> importResult, Long importDuration) {
		
		try {
			IndicatorImportHistoryEntity entity = new IndicatorImportHistoryEntity();
			entity.setImportType(importType);
			entity.setTemplateType(templateType);
			entity.setFileName(fileName);
			entity.setFileSize(fileSize);
			entity.setPeriod(period);
			entity.setImportDuration(importDuration);
			
			// 从导入结果中提取统计信息
			if (importResult != null) {
				Integer totalCount = (Integer) importResult.get("totalCount");
				Integer successCount = (Integer) importResult.get("successCount");
				Integer failCount = (Integer) importResult.get("failCount");
				Boolean success = (Boolean) importResult.get("success");
				
				entity.setTotalCount(totalCount != null ? totalCount : 0);
				entity.setSuccessCount(successCount != null ? successCount : 0);
				entity.setFailCount(failCount != null ? failCount : 0);
				
				// 确定导入状态
				if (success != null && success) {
					if (failCount != null && failCount > 0) {
						entity.setImportStatus("PARTIAL");
					} else {
						entity.setImportStatus("SUCCESS");
					}
				} else {
					entity.setImportStatus("FAILED");
				}
				
				// 保存完整的导入结果为JSON
				entity.setImportResult(objectMapper.writeValueAsString(importResult));
				
				// 提取错误信息
				if (importResult.containsKey("errors")) {
					Object errors = importResult.get("errors");
					if (errors != null) {
						entity.setErrorMessage(errors.toString());
					}
				}
			}
			
			// 保存记录
			this.save(entity);
			
			log.info("导入历史记录已保存，ID: {}, 文件名: {}, 状态: {}", 
				entity.getId(), fileName, entity.getImportStatus());
			
			return entity.getId();
			
		} catch (Exception e) {
			log.error("保存导入历史记录失败", e);
			return null;
		}
	}

}
