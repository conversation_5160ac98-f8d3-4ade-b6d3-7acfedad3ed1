/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.Map;

/**
 * 指标导入历史记录 服务类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface IIndicatorImportHistoryService extends BaseService<IndicatorImportHistoryEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param indicatorImportHistory
	 * @return
	 */
	IPage<IndicatorImportHistoryVO> selectIndicatorImportHistoryPage(IPage<IndicatorImportHistoryVO> page, IndicatorImportHistoryVO indicatorImportHistory);

	/**
	 * 根据搜索条件分页查询导入历史记录
	 *
	 * @param page 分页参数
	 * @param searchKeyword 搜索关键词（文件名、模板类型等）
	 * @param importType 导入类型
	 * @param templateType 模板类型
	 * @param importStatus 导入状态
	 * @param period 数据期间
	 * @return 分页结果
	 */
	IPage<IndicatorImportHistoryVO> selectIndicatorImportHistoryPageByCondition(
		IPage<IndicatorImportHistoryVO> page,
		String searchKeyword,
		String importType,
		String templateType,
		String importStatus,
		String period
	);

	/**
	 * 记录导入历史
	 *
	 * @param importType 导入类型
	 * @param templateType 模板类型
	 * @param fileName 文件名
	 * @param fileSize 文件大小
	 * @param period 数据期间
	 * @param importResult 导入结果
	 * @param importDuration 导入耗时
	 * @return 历史记录ID
	 */
	Long recordImportHistory(String importType, String templateType, String fileName, Long fileSize, 
		String period, Map<String, Object> importResult, Long importDuration);

}
